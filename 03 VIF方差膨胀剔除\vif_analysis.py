# -*- coding: utf-8 -*-
"""
本脚本提供一个全自动的VIF（方差膨胀因子）分析流程，用于筛选环境变量中的多重共线性。
主要功能包括：
1. 智能数据预处理：支持剖面数据聚合、自动处理常量列和缺失值。
2. 灵活分析模式：可对合并数据集进行统一分析，或对多期数据进行独立分析。
3. 过程透明化：记录并可视化VIF的迭代剔除过程。
4. 结果可视化与导出：生成VIF条形图和迭代过程图，并输出包含筛选结果的Excel报告。
"""

import pandas as pd
from statsmodels.stats.outliers_influence import variance_inflation_factor
import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# =============================================================================
# --- 1. 全局与绘图设置 ---
# =============================================================================
try:
    # 设置中文字体，以确保图表能正确显示中文标签
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except Exception:
    print("\n警告: 未能设置中文字体 'SimHei'。图表中的中文可能无法正常显示。")

# =============================================================================
# --- 2. 用户核心配置区 ---
# =============================================================================

# --- 分析模式 ---
# 'merged': 对合并后的1980和2023数据进行统一分析，筛选出一个公共的变量集。
# 'separate': 分别对1980和2023年的数据进行独立的VIF分析。
analysis_mode = 'merged'

# --- 文件路径 ---
# 'merged'模式下使用此路径
merged_file_path = r"E:\05 Python\Devway\01 硕士论文\00 原始数据\辅助变量-两期数据.xlsx"
# 'separate'模式下使用这两个路径
file_path_1980 = r"D:\Desktop\1980.xlsx"
file_path_2023 = r"D:\Desktop\2023.xlsx"

# --- 数据聚合配置 ---
# 是否将多层剖面数据按剖面ID聚合成单行记录
agg_mode = True
profile_id = 'ProfileID' # 用于聚合的唯一ID列

# --- VIF阈值 ---
# VIF大于该值的变量将在迭代中被剔除，通常设置为5或10
vif_threshold = 5

# --- 输出目录 ---
# 所有分析结果（Excel报告和图片）将保存到此目录
output_dir = r"E:\05 Python\Devway\01 硕士论文\00 原始数据\嫩江_VIF_筛选结果"

# --- 变量角色定义 ---

# 定义不参与VIF计算的变量
exclude_cols = [
    # 基础信息
    'ProfileID', 'Longitude', 'Latitude', 'City', 'location',
    'Soilclass', 'LandUse', 'year',
    # 剖面深度信息
    '深度范围', '深度中点',
    # 因变量 (Y)
    'pH', 'SOM', 'TN', 'TP', '物理粘粒', '物理砂粒',
    # 3D剖面变量 (与因变量类似，不作为宏观驱动力)
    'bdod', 'cec', 'Soil_Temperature_Mean', 'Soil_Water_Content_Mean'
]

# 注：不再使用白名单，改为使用除exclude_cols外的所有数值型变量

# =============================================================================
# --- 3. 核心功能函数 ---
# =============================================================================

def aggregate_data(df, id_col):
    """将多层剖面数据聚合为单行记录"""
    if not agg_mode:
        print("配置为跳过剖面数据聚合步骤。")
        return df

    if id_col not in df.columns:
        print(f"错误: 聚合ID列 '{id_col}' 不在数据中。", file=sys.stderr)
        return None

    group_keys = [id_col]
    if 'year' in df.columns:
        group_keys.append('year')
    else:
        print(f"警告: 未检测到 'year' 列。将仅按 '{id_col}' 进行聚合。")

    print(f"正在根据 {group_keys} 列对剖面数据进行聚合...")

    num_cols = df.select_dtypes(include=['number']).columns
    text_cols = df.select_dtypes(exclude=['number']).columns

    agg_funcs = {col: 'mean' for col in num_cols}
    agg_funcs.update({col: 'first' for col in text_cols})

    for key in group_keys:
        if key in agg_funcs:
            agg_funcs[key] = 'first'

    agg_df = df.groupby(group_keys, as_index=False).agg(agg_funcs)
    print(f"聚合完成。数据从 {len(df)} 行减少到 {len(agg_df)} 行。")
    return agg_df

def calc_vif(df_vars, threshold, year_label):
    """执行迭代VIF分析，自动处理数据问题并记录过程"""
    print("\n" + "="*80)
    print(f"开始对数据集 '{year_label}' 进行VIF分析...")
    print(f"将自动使用除exclude_cols外的所有数值型变量。")
    print(f"VIF阈值设定为: {threshold}")
    print("="*80 + "\n")

    vars_df = df_vars.copy()

    # 预处理：移除常量列
    const_cols = [col for col in vars_df.columns if vars_df[col].nunique(dropna=False) <= 1]
    if const_cols:
        print("*"*60)
        print(f"警告: 检测到 [{len(const_cols)}] 个常量列，已自动排除：")
        print(f"  -> {const_cols}")
        vars_df.drop(columns=const_cols, inplace=True)
        print(f"排除后，剩余有效变量数量: {vars_df.shape[1]}")
        print("*"*60 + "\n")

    if vars_df.shape[1] < 2:
        print("错误：有效变量少于2个，无法进行VIF分析。")
        return pd.DataFrame(), [], pd.DataFrame(), pd.DataFrame()

    # 在VIF迭代前，首先检查并处理相关系数为1或-1的变量对
    print("--- 步骤 1: 检查并处理完美共线性 (相关系数 ≈ ±1) ---")
    corr_mat = vars_df.corr()
    drop_cols = set()
    for i in range(len(corr_mat.columns)):
        for j in range(i):
            if np.isclose(corr_mat.iloc[i, j], 1.0) or np.isclose(corr_mat.iloc[i, j], -1.0):
                col_i = corr_mat.columns[i]
                col_j = corr_mat.columns[j]
                if col_i > col_j: drop_cols.add(col_i)
                else: drop_cols.add(col_j)
    if drop_cols:
        sorted_drop_cols = sorted(list(drop_cols))
        print(f"检测到完美共线性，自动移除: {sorted_drop_cols}")
        vars_df.drop(columns=sorted_drop_cols, inplace=True)
    else:
        print("未检测到完美共线性问题。")
    print("-" * 50)

    # 处理缺失值 (NaN) 和无穷大值 (inf)
    print("\n--- 步骤 2: 检查并处理缺失值 (NaN) ---")
    vars_df.replace([np.inf, -np.inf], np.nan, inplace=True)
    nan_cols = vars_df.columns[vars_df.isnull().any()].tolist()
    if nan_cols:
        print(f"警告: 在 {len(nan_cols)} 个列中检测到NaN，将用均值填充。")
        vars_df.fillna(vars_df.mean(), inplace=True)
        if vars_df.isnull().sum().sum() > 0:
            remain_nan_cols = vars_df.columns[vars_df.isnull().any()].tolist()
            print(f"均值填充后，以下列仍含NaN（可能整列为空），将被移除：{remain_nan_cols}")
            vars_df.dropna(axis=1, how='any', inplace=True)
        print("缺失值填充完成。")
    else:
        print("未检测到缺失值。")
    print("-" * 50)

    if vars_df.shape[1] < 2:
        print("错误：处理数据后，有效变量少于2个，无法进行VIF分析。")
        return pd.DataFrame(), [], pd.DataFrame(), pd.DataFrame()

    print("\n--- 步骤 3: 开始迭代VIF分析 ---")
    vif_history = []
    iter_num = 0
    while True:
        iter_num += 1
        var_count = vars_df.shape[1]
        vif_data = pd.DataFrame()
        vif_data["feature"] = vars_df.columns
        try:
            vif_data["VIF"] = [variance_inflation_factor(vars_df.values, i) for i in range(var_count)]
        except Exception as e:
            print(f"\n严重错误: VIF计算失败。 {e}", file=sys.stderr)
            return pd.DataFrame(), [], pd.DataFrame(), pd.DataFrame()
        vif_data = vif_data.sort_values(by='VIF', ascending=False).reset_index(drop=True)
        max_vif = vif_data['VIF'].iloc[0]
        max_var = vif_data['feature'].iloc[0]
        print(f"轮次 {iter_num} | 变量数: {var_count} | 最大VIF: {max_vif:.4f} (在 '{max_var}' 上)")
        history_entry = {'iteration': iter_num, 'max_vif': max_vif, 'removed_variable': max_var if max_vif >= threshold else 'None'}
        vif_history.append(history_entry)
        if max_vif < threshold:
            print(f"\n分析完成！所有剩余变量的VIF值均低于 {threshold}。")
            final_vars = vars_df.columns.tolist()
            print("最终筛选出的变量列表：")
            print(final_vars)
            break
        else:
            vars_df = vars_df.drop(columns=[max_var])
            if vars_df.shape[1] < 2:
                print("变量数量少于2，停止迭代。")
                final_vars = vars_df.columns.tolist()
                break
    final_vif = vif_data.sort_values(by='VIF').reset_index(drop=True)
    history_df = pd.DataFrame(vif_history)
    return vars_df, final_vars, final_vif, history_df

def create_vif_plots(final_vif, vif_history, year_label, output_dir, threshold):
    """生成并保存VIF分析过程的两张核心可视化图表。"""
    print("\n--- 正在生成可视化图表 ---")

    # 图1: 最终入选变量的VIF值条形图-升序
    plt.figure(figsize=(12, 8))
    final_vif_sorted = final_vif.sort_values(by='VIF', ascending=True)
    sns.barplot(x='VIF', y='feature', data=final_vif_sorted, hue='feature', palette='viridis', legend=False)
    plt.axvline(x=threshold, color='r', linestyle='--', label=f'VIF阈值 = {threshold}')
    plt.title(f'VIF值条形图', fontsize=24)
    plt.xlabel('方差膨胀因子 (VIF)', fontsize=16)
    plt.ylabel('环境变量', fontsize=16)
    plt.tick_params(axis='both', which='major', labelsize=16)  # 设置刻度字体大小
    plt.legend(fontsize=16)  # 设置图例字体大小
    plt.tight_layout()
    plot1_path = os.path.join(output_dir, f"最终入选变量的VIF值条形图.png")
    try:
        plt.savefig(plot1_path, dpi=500)
        print(f"图表已保存: {plot1_path}")
    except Exception as e:
        print(f"保存图表1时出错: {e}", file=sys.stderr)
    plt.close()

    # 图2: VIF迭代剔除过程图 
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12), height_ratios=[3, 1])

    # 主图：VIF变化曲线
    ax1.plot(vif_history['iteration'], vif_history['max_vif'], marker='o', linestyle='-',
             color='b', linewidth=2, markersize=8, label='每轮最大VIF值')
    ax1.axhline(y=threshold, color='r', linestyle='--', linewidth=2, label=f'VIF阈值 = {threshold}')
    ax1.set_yscale('log')
    ax1.set_title('VIF迭代剔除过程', fontsize=20, pad=20)
    ax1.set_ylabel('最大VIF值 (对数尺度)', fontsize=16)
    ax1.tick_params(axis='both', which='major', labelsize=14)
    ax1.grid(True, linestyle='--', alpha=0.6)
    ax1.legend(fontsize=14, loc='upper right')

    # 下方表格：显示剔除的变量
    removed_vars = []
    iterations = []
    for i, row in vif_history.iterrows():
        if row['removed_variable'] != 'None':
            removed_vars.append(row['removed_variable'])
            iterations.append(row['iteration'])

    if removed_vars:
        # 创建表格数据 - 按列分组显示
        rows_per_col = 12  # 每列显示12行
        num_cols = (len(removed_vars) + rows_per_col - 1) // rows_per_col  # 计算需要的列数

        # 创建列标题
        col_labels = []
        for col in range(num_cols):
            start_idx = col * rows_per_col
            end_idx = min(start_idx + rows_per_col, len(removed_vars))
            if start_idx < len(removed_vars):
                col_labels.append(f"剔除变量 ({start_idx+1}-{end_idx})")

        # 创建表格数据
        table_data = []
        for row in range(rows_per_col):
            row_data = []
            for col in range(num_cols):
                idx = col * rows_per_col + row
                if idx < len(removed_vars):
                    row_data.append(f"第{iterations[idx]}轮: {removed_vars[idx]}")
                else:
                    row_data.append('')  # 空单元格
            table_data.append(row_data)

        # 在下方子图中显示表格
        ax2.axis('off')
        table = ax2.table(cellText=table_data,
                         colLabels=col_labels,
                         cellLoc='left',  # 左对齐，便于阅读长变量名
                         loc='center',
                         colWidths=[1.0/len(col_labels)] * len(col_labels))
        table.auto_set_font_size(False)
        table.set_fontsize(14)  # 适中的字体大小
        table.scale(1, 2.5)  # 调整表格高度

        # 设置表格样式
        for i in range(len(table_data) + 1):
            for j in range(len(col_labels)):
                cell = table[(i, j)]
                if i == 0:  # 表头
                    cell.set_facecolor('#4CAF50')
                    cell.set_text_props(weight='bold', color='white')
                else:  # 数据行
                    if table_data[i-1][j]:  # 非空单元格
                        cell.set_facecolor('#f0f0f0' if i % 2 == 0 else 'white')
                    else:  # 空单元格
                        cell.set_facecolor('white')
    else:
        ax2.axis('off')
        ax2.text(0.5, 0.5, '无变量被剔除', ha='center', va='center', fontsize=16)

    ax1.set_xlabel('迭代轮次', fontsize=16)
    plt.tight_layout()
    plot2_path = os.path.join(output_dir, f"VIF迭代剔除过程图.png")
    try:
        plt.savefig(plot2_path, dpi=500)
        print(f"图表已保存: {plot2_path}")
    except Exception as e:
        print(f"保存图表2时出错: {e}", file=sys.stderr)
    plt.close()

def process_dataset(file_path, year_label):
    """封装了从数据加载到结果保存的完整VIF分析流程。"""
    print("\n" + "#"*80)
    print(f"# 开始处理: {year_label} ({os.path.basename(file_path)})")
    print("#"*80 + "\n")
    try:
        orig_df = pd.read_excel(file_path,sheet_name=1)
        print(f"成功加载数据: {orig_df.shape[0]} 行, {orig_df.shape[1]} 列。")
    except FileNotFoundError:
        print(f"错误：文件未找到于 '{file_path}'。", file=sys.stderr)
        return
    except Exception as e:
        print(f"读取Excel文件时出错: {e}", file=sys.stderr)
        return
    if agg_mode:
        df_for_vif = aggregate_data(orig_df.copy(), profile_id)
        if df_for_vif is None: return
    else:
         print(f"错误：聚合失败。")

    # --- 核心变量筛选逻辑：使用除exclude_cols外的所有数值型变量 ---
    non_pred_cols = [col for col in exclude_cols if col in df_for_vif.columns]
    print("\n识别到以下非预测变量(将不参与VIF计算):")
    print(f"  -> {non_pred_cols}")
    
    # 获取除exclude_cols外的所有数值型变量
    all_numeric_cols = df_for_vif.select_dtypes(include=['number']).columns.tolist()
    available_predictors = [col for col in all_numeric_cols if col not in exclude_cols]
    
    print(f"\n数据集中共有 {len(all_numeric_cols)} 个数值型变量")
    print(f"排除 {len(non_pred_cols)} 个非预测变量后，剩余 {len(available_predictors)} 个变量进入VIF分析")
    
    df_vars = df_for_vif[available_predictors]
    print(f"最终用于VIF分析的变量数量: {df_vars.shape[1]}")
    if len(available_predictors) < len(all_numeric_cols):
        excluded_vars = set(all_numeric_cols) - set(available_predictors)
        print(f"被排除的变量: {sorted(list(excluded_vars))}")

    # 对变量列进行字母排序，以确保结果唯一
    df_vars = df_vars[sorted(df_vars.columns)]
    print("已对变量列进行字母排序。")
    final_vars_df, final_cols, final_vif, vif_history = calc_vif(df_vars, vif_threshold, year_label)
    if not final_cols:
        print(f"\n在数据集 '{year_label}' 中，未能筛选出任何满足条件的变量。")
        return

    # --- 构建并保存最终输出 ---
    final_non_pred_cols = [col for col in exclude_cols if col in orig_df.columns]
    all_cols_to_keep = list(dict.fromkeys(final_non_pred_cols + final_cols))
    all_cols_safe = [col for col in all_cols_to_keep if col in orig_df.columns]
    output_df = orig_df[all_cols_safe]
    print("\n正在从原始数据中构建最终输出表...")
    print(f"最终输出表维度: {output_df.shape[0]} 行, {output_df.shape[1]} 列。")
    os.makedirs(output_dir, exist_ok=True)
    output_filename = os.path.join(output_dir, f"VIF_筛选结果.xlsx")
    try:
        with pd.ExcelWriter(output_filename, engine='openpyxl') as writer:
            output_df.to_excel(writer, sheet_name='筛选后数据', index=False)
            final_vif.to_excel(writer, sheet_name='最终VIF值汇总', index=False)
        print(f"\n结果已成功保存至: {output_filename}")
        create_vif_plots(final_vif, vif_history, year_label, output_dir, vif_threshold)
    except Exception as e:
        print(f"保存结果至 '{output_filename}' 时出错: {e}", file=sys.stderr)
    return output_df, final_cols

# =============================================================================
# --- 4. 主执行逻辑 ---
# =============================================================================
if __name__ == "__main__":
    print("VIF分析脚本启动...")
    print(f"结果将保存至目录: '{os.path.abspath(output_dir)}'")
    if analysis_mode == 'merged':
        print("\n" + "="*80)
        print("模式: 合并分析 ('merged')")
        print("="*80)
        process_dataset(merged_file_path, "merged_1980_2023")
    elif analysis_mode == 'separate':
        print("\n" + "="*80)
        print("模式: 分开分析 ('separate')")
        print("="*80)
        process_dataset(file_path_1980, "1980")
        process_dataset(file_path_2023, "2023")
    else:
        print(f"错误：无效的 ANALYSIS_MODE: '{analysis_mode}'。", file=sys.stderr)
    print("\n脚本执行完毕。") 



